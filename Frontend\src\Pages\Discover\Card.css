:root {
  --cyan: #3bb4a1;
  --light-grey: #6d6e70;
  --grey: #2d2d2d;
  --yellow: #fbf1a4;
  --dark-cyan: #013e38;
  --red: #f56664;
  --basefont: "<PERSON>", sans-serif;
  --secfont: "<PERSON><PERSON><PERSON>", sans-serif;
}

h3 {
  font-family: var(--secfont);
  margin: 10px 0;
}

h6 {
  font-family: var(--secfont);
  font-size: 1rem !important;
  margin: 5px 0;
  text-transform: uppercase;
}

p {
  font-family: var(--secfont);
  font-size: 14px;
  line-height: 21px;
}

.card-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  font-family: var(--secfont);
  background-color: #00000050;
  border-radius: 25px;
  box-shadow: 10px 10px 15px rgba(0, 0, 0, 0.35);
  color: #b3c2cd;
  position: relative;
  width: 300px;
  min-width: 300px;
  height: 450px;
  max-width: 100%;
  text-align: center;
  margin: 3rem;
  overflow: hidden;
}

.card-container img {
  border: 1px solid #fbf1a4;
  border-radius: 50%;
  padding: 7px;
  height: 100px;
  width: 100px;
  margin: 1rem;
}

.prof-buttons {
  display: flex;
  justify-content: space-around;
}

button.primary {
  margin: 1rem;
  background-color: #3bb4a1;
  border: 1px solid #3bb4a1;
  border-radius: 3px;
  color: #fff;
  font-family: Montserrat, sans-serif;
  font-weight: 500;
  padding: 0.4rem;
  transition: 0.3s ease-in-out;
}

button.primary.ghost {
  margin: 1rem;
  background-color: transparent;
  color: #3bb4a1;
  transition: 0.3s ease-in-out;
}

button.primary:hover {
  background-color: #fff;
  color: #3bb4a1;
}

button.primary.ghost:hover {
  background-color: #ffffff27;
}

.profskills {
  background-color: rgba(48, 88, 100, 0.284);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.3rem;
  text-align: left;
  margin-top: 1rem;
  height: 15vh;
  width: 100%;
  bottom: 0;
  overflow-y: auto;
}

.profskill-boxes {
  margin: 1rem;
  display: flex;
  flex-wrap: wrap;
}

.profskill-box {
  text-align: center;
  background-color: #809c5c;
  color: white;
  padding: 0.5rem;
  margin-right: 1rem;
  margin-top: 1rem;
  border-radius: 5px;
  min-width: 4rem;
}

@media (max-width: 768px) {
  .card-container {
    width: 50vw;
    height: 50vh;
  }
}
