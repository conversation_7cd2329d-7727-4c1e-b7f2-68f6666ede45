{"name": "skillswap_backend", "version": "1.0.0", "description": "This is the backend of skillswap project.", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js"}, "author": "", "license": "ISC", "dependencies": {"cloudinary": "^2.0.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.13", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "socket.io": "^4.7.5"}, "devDependencies": {"nodemon": "^3.0.3", "prettier": "^3.2.5"}}