import express from "express";
import { createChat, getChats } from "../controllers/chat.controllers.js";
import { verifyJWT_username } from "../middlewares/verifyJWT.middleware.js";

const router = express.Router();

// Root route for testing (without authentication)
router.get("/test", (req, res) => {
    res.json({
        message: "Chat API endpoints",
        note: "All chat routes require authentication (JWT token)",
        availableRoutes: {
            createChat: "POST /chat/",
            getChats: "GET /chat/",
            note: "Use /chat/test for testing without authentication"
        }
    });
});

router.post("/", verifyJWT_username, createChat);
router.get("/", verifyJWT_username, getChats);
// router.get("/:id", verifyJWT_username, getChatById);

export default router;
