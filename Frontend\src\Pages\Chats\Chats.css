.container-overall {
  background-color: #2d2d2d;
  min-height: 90vh;
  font-family: Montserrat, sans-serif;
  color: white;
  border-right: 1px solid #3bb4a1;
}

.container-right {
  display: flex;
  background-color: lightgrey;
}

.container-left {
  flex: 3;
  background-color: #2d2d2d;
  min-height: 90vh;
}

.tabs {
  display: flex;
  padding-top: 2rem;
  justify-content: space-around;
  border-bottom: 1px solid #3bb4a1;
}

.chat-list {
  padding: 10px;
}

.modalBG {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
}

.modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #2d2d2d;
  color: #3bb4a1;
  padding: 50px;
  border-radius: 10px;
  margin: 5rem;
}

.container-chat {
  min-width: 70vw;
}

.modalContent {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #2d2d2d;
  color: #3bb4a1;
  padding: 50px;
  border-radius: 10px;
}

@media (max-width: 780px) {
  .container-right {
    display: flex;
    flex-direction: column;
  }
  .modalContent {
    padding: 0px;
  }
}
