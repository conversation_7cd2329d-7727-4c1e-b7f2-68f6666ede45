import { Router } from "express";
import {
  googleAuthCallback,
  googleAuthHandler,
  handleGoogleLoginCallback,
  handleLogout,
} from "../controllers/auth.controllers.js";

const router = Router();

// Root route for testing
router.get("/", (req, res) => {
  res.json({
    message: "Auth API endpoints",
    availableRoutes: {
      googleLogin: "/auth/google",
      googleCallback: "/auth/google/callback",
      logout: "/auth/logout"
    }
  });
});

router.get("/google", googleAuthHandler);
router.get("/google/callback", googleAuthCallback, handleGoogleLoginCallback);
router.get("/logout", handleLogout);

export default router;
