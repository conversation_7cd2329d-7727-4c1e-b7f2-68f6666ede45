{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.7", "bootstrap": "^5.3.3", "mongodb": "^6.17.0", "react": "^18.2.0", "react-bootstrap": "^2.10.1", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-router-dom": "^6.22.1", "react-scrollable-feed": "^2.0.1", "react-toastify": "^10.0.4", "socket.io-client": "^4.7.5", "uuid": "^9.0.1"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.2.5", "vite": "^5.1.0"}}